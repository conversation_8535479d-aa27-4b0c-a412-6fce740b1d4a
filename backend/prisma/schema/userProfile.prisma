enum Gender {
  MALE
  FEMALE
  OTHER
}

enum UserProfileStatus {
  active
  pending
  disabled
  inactive
  invited
}

model UserProfile {
  id                String            @id @default(uuid()) @map("id") @db.Uuid
  userId            String            @map("user_id") @db.Uuid
  roleId            String            @map("role_id") @db.Uuid
  firstName         String?           @map("first_name")
  lastName          String?           @map("last_name")
  cityId            String?           @map("city") @db.Uuid
  referralCode      String?           @map("referral_code")
  profilePictureUrl String?           @map("profile_picture_url")
  languageId        String?           @map("language_id") @db.Uuid
  gender            Gender?           @map("gender")
  status            UserProfileStatus @default(pending) @map("status")
  dob               DateTime?         @map("dob") @db.Date
  isOnline          Boolean           @default(false) @map("is_online")
  rideOtp           String?           @map("ride_otp")
  inviteExpiresAt   DateTime?         @map("invite_expires_at") @db.Timestamptz
  createdAt         DateTime          @default(now()) @map("created_at")
  updatedAt         DateTime          @updatedAt @map("updated_at")
  deletedAt         DateTime?         @map("deleted_at") @db.Timestamptz

  // Relations
  user                          User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  role                          Role                    @relation(fields: [roleId], references: [id])
  city                          City?                   @relation(fields: [cityId], references: [id], onDelete: SetNull)
  language                      Language?               @relation(fields: [languageId], references: [id], onDelete: SetNull)
  driverVehicles                DriverVehicle[]
  driverKycs                    DriverKyc[]
  driverCityProducts            DriverCityProduct[]
  ridesAsDriver                 Ride[]                  @relation("RideDriver")
  ridesAsRider                  Ride[]                  @relation("RideRider")
  createdDriverKycs             DriverKyc[]             @relation(name: "DriverKycCreatedBy")
  createdDriverVehicleDocuments DriverVehicleDocument[] @relation(name: "DriverVehicleDocumentCreatedBy")
  favoriteLocation              FavoriteLocation[]
  rideOffersAsDriver            RideOffer[]             @relation("RideOfferDriver")
  createdDriverVehicle          DriverVehicle[]         @relation(name: "DriverVehicleCreatedBy")
  reviewsAsRider                Review[]                @relation("ReviewRider")
  reviewsAsDriver               Review[]                @relation("ReviewDriver")
  reviewsWritten                Review[]                @relation("ReviewBy")
  metaData                      UserMetaData?
  cityAdmins                    CityAdmin[]
  rideLifecycles                RideLifecycle[]

  @@map("user_profiles")
}
