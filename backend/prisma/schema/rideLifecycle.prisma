model RideLifecycle {
  id        String    @id @default(uuid()) @map("id") @db.Uuid
  rideId    String    @map("ride_id") @db.Uuid
  driverId  String?   @map("driver_id") @db.Uuid
  status    String    @map("status")
  meta      Json?     @map("meta") @db.JsonB
  location  Json?     @map("location") @db.JsonB
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  ride   Ride         @relation(fields: [rideId], references: [id], onDelete: Cascade)
  driver UserProfile? @relation(fields: [driverId], references: [id], onDelete: SetNull)

  @@index([rideId], name: "idx_ride_lifecycle_ride_id")
  @@index([status], name: "idx_ride_lifecycle_status")
  @@index([createdAt], name: "idx_ride_lifecycle_created_at")
  @@index([driverId], name: "idx_ride_lifecycle_driver_id")
  @@map("ride_lifecycles")
}
