import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { AppConfigService } from '../../config/config.service';
import axios, { AxiosResponse } from 'axios';

export interface Location {
  lat: number;
  lng: number;
}

export interface RouteMatrixRequest {
  origins: Location[];
  destinations: Location[];
  travelMode?: 'DRIVE' | 'WALK' | 'BICYCLE' | 'TRANSIT';
  routingPreference?:
    | 'TRAFFIC_UNAWARE'
    | 'TRAFFIC_AWARE'
    | 'TRAFFIC_AWARE_OPTIMAL';
  units?: 'IMPERIAL' | 'METRIC';
}

export interface RouteMatrixElement {
  duration: {
    seconds: number;
    text: string;
  };
  distance: {
    meters: number;
    text: string;
  };
  status: 'OK' | 'NOT_FOUND' | 'ZERO_RESULTS';
  condition?: 'ROUTE_EXISTS' | 'ROUTE_NOT_FOUND';
}

export interface RouteMatrixResponse {
  originIndex: number;
  destinationIndex: number;
  status: string;
  condition?: string | undefined;
  duration?:
    | {
        seconds: number;
      }
    | undefined;
  distanceMeters?: number | undefined;
  staticDuration?:
    | {
        seconds: number;
      }
    | undefined;
  localizedValues?:
    | {
        distance: {
          text: string;
        };
        duration: {
          text: string;
        };
        staticDuration: {
          text: string;
        };
      }
    | undefined;
}

export interface GoogleRouteMatrixApiResponse {
  originIndex?: number;
  destinationIndex?: number;
  status?: string;
  condition?: string;
  duration?: {
    seconds: number;
  };
  distanceMeters?: number;
  staticDuration?: {
    seconds: number;
  };
  localizedValues?: {
    distance: {
      text: string;
    };
    duration: {
      text: string;
    };
    staticDuration: {
      text: string;
    };
  };
}

export interface ComputeRoutesRequest {
  origin: Location;
  destination: Location;
  intermediates?: Location[];
  travelMode?: 'DRIVE' | 'WALK' | 'BICYCLE' | 'TRANSIT';
  routingPreference?:
    | 'TRAFFIC_UNAWARE'
    | 'TRAFFIC_AWARE'
    | 'TRAFFIC_AWARE_OPTIMAL';
}

export interface ComputeRoutesResponse {
  distanceMeters: number;
  duration: string; // e.g., "111s"
  durationSeconds: number; // parsed duration in seconds
}

export interface GoogleComputeRoutesApiResponse {
  routes: Array<{
    legs: Array<{
      distanceMeters: number;
      duration: string;
      staticDuration: string;
      polyline: {
        encodedPolyline: string;
      };
      startLocation: {
        latLng: {
          latitude: number;
          longitude: number;
        };
      };
      endLocation: {
        latLng: {
          latitude: number;
          longitude: number;
        };
      };
    }>;
    distanceMeters: number;
    duration: string;
  }>;
}

@Injectable()
export class GoogleRouteMatrixService {
  private readonly logger = new Logger(GoogleRouteMatrixService.name);
  private readonly baseUrl =
    'https://routes.googleapis.com/distanceMatrix/v2:computeRouteMatrix';
  private readonly routesUrl =
    'https://routes.googleapis.com/directions/v2:computeRoutes';

  constructor(private readonly configService: AppConfigService) {}

  /**
   * Compute route matrix using Google Routes API
   * @param request Route matrix request parameters
   * @returns Promise<RouteMatrixResponse[]>
   */
  async computeRouteMatrix(
    request: RouteMatrixRequest,
  ): Promise<RouteMatrixResponse[]> {
    try {
      this.logger.log(
        `Computing route matrix for ${request.origins.length} origins and ${request.destinations.length} destinations`,
      );

      const apiKey = this.configService.googleMapsApiKey;

      // Prepare request body according to Google Routes API v2 format
      const requestBody = {
        origins: request.origins.map((origin) => ({
          waypoint: {
            location: {
              latLng: {
                latitude: origin.lat,
                longitude: origin.lng,
              },
            },
          },
        })),
        destinations: request.destinations.map((destination) => ({
          waypoint: {
            location: {
              latLng: {
                latitude: destination.lat,
                longitude: destination.lng,
              },
            },
          },
        })),
        travelMode: request.travelMode || 'DRIVE',
        routingPreference: request.routingPreference || 'TRAFFIC_AWARE',
        units: request.units || 'METRIC',
      };

      const response: AxiosResponse<GoogleRouteMatrixApiResponse[]> =
        await axios.post(this.baseUrl, requestBody, {
          headers: {
            'Content-Type': 'application/json',
            'X-Goog-Api-Key': apiKey,
            'X-Goog-FieldMask':
              'originIndex,destinationIndex,status,condition,duration,distanceMeters,staticDuration,localizedValues.distance,localizedValues.duration,localizedValues.staticDuration',
          },
          timeout: 30000, // 30 seconds timeout
        });

      this.logger.log(
        `Route matrix API response received with ${response.data.length} elements`,
      );

      return response.data.map((element) => ({
        originIndex: element.originIndex || 0,
        destinationIndex: element.destinationIndex || 0,
        status: element.status || 'UNKNOWN',
        condition: element.condition,
        duration: element.duration,
        distanceMeters: element.distanceMeters,
        staticDuration: element.staticDuration,
        localizedValues: element.localizedValues,
      }));
    } catch (error: any) {
      this.logger.error('Failed to compute route matrix', error);

      if (error.response) {
        this.logger.error(
          `API Error: ${error.response.status} - ${error.response.data?.error?.message || 'Unknown error'}`,
        );
        throw new BadRequestException(
          `Google Routes API error: ${error.response.data?.error?.message || 'Unknown error'}`,
        );
      }

      throw new BadRequestException('Failed to compute route matrix');
    }
  }

  /**
   * Compute route matrix for drivers to pickup location
   * @param driverLocations Array of driver locations
   * @param pickupLocation Pickup location
   * @returns Promise<RouteMatrixResponse[]>
   */
  async computeDriversToPickup(
    driverLocations: Location[],
    pickupLocation: Location,
  ): Promise<RouteMatrixResponse[]> {
    if (driverLocations.length === 0) {
      return [];
    }

    return this.computeRouteMatrix({
      origins: driverLocations,
      destinations: [pickupLocation],
      travelMode: 'DRIVE',
      routingPreference: 'TRAFFIC_AWARE',
    });
  }

  /**
   * Compute route matrix from pickup to destination
   * @param pickupLocation Pickup location
   * @param destinationLocation Destination location
   * @returns Promise<RouteMatrixResponse>
   */
  async computePickupToDestination(
    pickupLocation: Location,
    destinationLocation: Location,
  ): Promise<RouteMatrixResponse | null> {
    const results = await this.computeRouteMatrix({
      origins: [pickupLocation],
      destinations: [destinationLocation],
      travelMode: 'DRIVE',
      routingPreference: 'TRAFFIC_AWARE',
    });

    return results.length > 0 ? results[0] : null;
  }

  /**
   * Batch compute route matrices with rate limiting
   * @param requests Array of route matrix requests
   * @param batchSize Number of requests to process at once
   * @param delayMs Delay between batches in milliseconds
   * @returns Promise<RouteMatrixResponse[][]>
   */
  async batchComputeRouteMatrix(
    requests: RouteMatrixRequest[],
    batchSize: number = 5,
    delayMs: number = 1000,
  ): Promise<RouteMatrixResponse[][]> {
    const results: RouteMatrixResponse[][] = [];

    for (let i = 0; i < requests.length; i += batchSize) {
      const batch = requests.slice(i, i + batchSize);
      const batchPromises = batch.map((request) =>
        this.computeRouteMatrix(request),
      );

      try {
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);

        // Add delay between batches to respect rate limits
        if (i + batchSize < requests.length) {
          await new Promise((resolve) => setTimeout(resolve, delayMs));
        }
      } catch (error) {
        this.logger.error(
          `Batch processing failed for batch starting at index ${i}`,
          error,
        );
        throw error;
      }
    }

    return results;
  }

  /**
   * Compute route with intermediates using Google Routes API v2
   * @param request Route request with origin, destination, and optional intermediates
   * @returns Promise<ComputeRoutesResponse>
   */
  async computeRoutes(
    request: ComputeRoutesRequest,
  ): Promise<ComputeRoutesResponse> {
    try {
      this.logger.log(`Computing route`);

      const apiKey = this.configService.googleMapsApiKey;

      // Prepare request body according to Google Routes API v2 format
      const requestBody: any = {
        origin: {
          location: {
            latLng: {
              latitude: request.origin.lat,
              longitude: request.origin.lng,
            },
          },
        },
        destination: {
          location: {
            latLng: {
              latitude: request.destination.lat,
              longitude: request.destination.lng,
            },
          },
        },
        travelMode: request.travelMode || 'DRIVE',
        routingPreference: request.routingPreference || 'TRAFFIC_AWARE',
      };

      // Add intermediates (stops) if they exist
      if (request.intermediates && request.intermediates.length > 0) {
        requestBody.intermediates = request.intermediates.map(
          (intermediate) => ({
            location: {
              latLng: {
                latitude: intermediate.lat,
                longitude: intermediate.lng,
              },
            },
          }),
        );
      }

      this.logger.debug(
        'Sending request to Google Routes API:',
        JSON.stringify(requestBody, null, 2),
      );

      const response: AxiosResponse<GoogleComputeRoutesApiResponse> =
        await axios.post(this.routesUrl, requestBody, {
          headers: {
            'Content-Type': 'application/json',
            'X-Goog-Api-Key': apiKey,
            'X-Goog-FieldMask':
              'routes.duration,routes.distanceMeters,routes.legs.duration,routes.legs.distanceMeters',
          },
          timeout: 30000, // 30 seconds timeout
        });

      this.logger.log(`Routes API response received`);
      this.logger.debug(
        'Full API response:',
        JSON.stringify(response.data, null, 2),
      );

      if (
        !response.data ||
        !response.data.routes ||
        response.data.routes.length === 0
      ) {
        this.logger.error('No routes found in API response:', response.data);
        throw new BadRequestException(
          'No routes found for the given locations',
        );
      }

      this.logger.log(`Found ${response.data.routes.length} routes`);

      const route = response.data.routes[0];

      if (!route.distanceMeters || !route.duration) {
        this.logger.error('Invalid route data:', route);
        throw new BadRequestException(
          'Invalid route data received from Google API',
        );
      }

      // Parse duration from string format (e.g., "111s") to seconds
      const durationSeconds = this.parseDurationToSeconds(route.duration);

      const result: ComputeRoutesResponse = {
        distanceMeters: route.distanceMeters,
        duration: route.duration,
        durationSeconds,
      };

      this.logger.log(
        `Route computed: ${result.distanceMeters}m, ${result.duration} (${result.durationSeconds}s)`,
      );

      return result;
    } catch (error: any) {
      this.logger.error('Failed to compute route', error);

      if (error.response) {
        this.logger.error(
          `API Error: ${error.response.status} - ${error.response.data?.error?.message || 'Unknown error'}`,
        );
        throw new BadRequestException(
          `Google Routes API error: ${error.response.data?.error?.message || 'Unknown error'}`,
        );
      }

      throw new BadRequestException('Failed to compute route');
    }
  }

  /**
   * Compute route distance and duration with optional stops
   * @param origin Origin location
   * @param destination Destination location
   * @param stops Optional array of intermediate stops
   * @returns Promise<{ distance: number; duration: number }> - distance in meters, duration in seconds
   */
  async computeDistanceAndDuration(
    origin: Location,
    destination: Location,
    stops?: Location[],
  ): Promise<{ distance: number; duration: number }> {
    const request: ComputeRoutesRequest = {
      origin,
      destination,
      travelMode: 'DRIVE',
      routingPreference: 'TRAFFIC_AWARE',
    };

    if (stops && stops.length > 0) {
      request.intermediates = stops;
    }

    const result = await this.computeRoutes(request);

    return {
      distance: result.distanceMeters, // Keep distance in meters
      duration: result.durationSeconds,
    };
  }

  /**
   * Parse duration string to seconds
   * @param duration Duration string (e.g., "111s", "2m30s", "1h15m30s")
   * @returns Duration in seconds
   */
  private parseDurationToSeconds(duration: string): number {
    if (!duration) return 0;

    // Handle simple seconds format (e.g., "111s")
    if (
      duration.endsWith('s') &&
      !duration.includes('m') &&
      !duration.includes('h')
    ) {
      return parseInt(duration.slice(0, -1), 10);
    }

    // Handle complex duration formats (e.g., "1h15m30s", "2m30s")
    let totalSeconds = 0;

    // Extract hours
    const hoursMatch = duration.match(/(\d+)h/);
    if (hoursMatch) {
      totalSeconds += parseInt(hoursMatch[1], 10) * 3600;
    }

    // Extract minutes
    const minutesMatch = duration.match(/(\d+)m/);
    if (minutesMatch) {
      totalSeconds += parseInt(minutesMatch[1], 10) * 60;
    }

    // Extract seconds
    const secondsMatch = duration.match(/(\d+)s/);
    if (secondsMatch) {
      totalSeconds += parseInt(secondsMatch[1], 10);
    }

    return totalSeconds;
  }
}
