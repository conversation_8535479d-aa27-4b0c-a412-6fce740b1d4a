/**
 * Core standardized interfaces for ride-matching system
 * This file consolidates and standardizes all common interfaces
 */

import {
  DriverStatus,
  RideStatus,
  BatchStatus,
  CancellationSource,
  RideMode,
  UrgencyLevel,
} from '../constants';

// =============================================================================
// LOCATION INTERFACES
// =============================================================================

/**
 * Standard location point interface used throughout the system
 */
export interface LocationPoint {
  lat: number;
  lng: number;
  address?: string;
}

/**
 * Detailed location with additional metadata
 */
export interface DetailedLocation extends LocationPoint {
  address: string;
  placeId?: string;
  zoneId?: string;
}

// =============================================================================
// FARE AND ESTIMATION INTERFACES
// =============================================================================

/**
 * Standard fare estimate breakdown
 */
export interface FareEstimate {
  baseFare: number;
  distanceFare: number;
  timeFare: number;
  totalFare: number;
  currency?: string;
}

/**
 * Standard estimated arrival information
 */
export interface EstimatedArrival {
  durationSeconds: number;
  distanceMeters: number;
  arrivalTime: string; // ISO string
}

/**
 * ETA information for driver to pickup
 */
export interface EtaToPickup {
  durationSeconds: number;
  distanceMeters: number;
}

// =============================================================================
// DRIVER INTERFACES
// =============================================================================

/**
 * Core driver metadata interface
 */
export interface DriverMetadata {
  driverId: string;
  status: DriverStatus;
  lat: number;
  lon: number;
  rating: number;
  acceptRate: number;
  lifetimeRides: number;
  headingHome: boolean;
  homeLat?: number;
  homeLon?: number;
  endingDropLat?: number;
  endingDropLon?: number;
  zoneId?: string;
  lastIdleAt: string; // ISO timestamp
  heading?: number; // Direction in degrees
}

/**
 * Driver candidate for matching with additional context
 */
export interface DriverCandidate {
  driverId: string;
  metadata: DriverMetadata;
  location: LocationPoint;
  etaToPickup?: EtaToPickup;
  eligibleProducts: string[]; // Product IDs this driver can serve
}

/**
 * Driver penalty information
 */
export interface DriverPenalty {
  driverId: string;
  rideId: string;
  batchNumber: number;
  penaltyPoints: number;
  appliedAt: Date;
  expiresAt: Date;
}

/**
 * Comprehensive driver score with breakdown
 */
export interface DriverScore {
  driverId: string;
  baseScore: number; // Original scoring points
  penaltyScore: number; // Negative points from penalties
  totalScore: number; // baseScore + penaltyScore
  breakdown: {
    // Core factors (135 points max)
    eta: number; // 0-40 points
    idleTime: number; // 0-20 points
    endingNearby: number; // 0-12 points
    acceptRate: number; // 0-15 points
    rating: number; // 0-12 points
    zoneTransition: number; // 0-8 points
    goingHomeAlign: number; // 0-10 points
    newDriverBonus: number; // 0-6 points

    // Penalty factors (negative points)
    batchPenalty: number; // 0 to -45 points
    recentRejections: number; // 0 to -10 points
  };
  metadata: {
    etaMinutes: number;
    idleMinutes: number;
    endingDistanceKm: number;
    isZoneTransition: boolean;
    isGoingHomeAligned: boolean;
    isNewDriver: boolean;
    appliedPenalties: DriverPenalty[];
    penaltyReason: string[];
    urgency?: UrgencyLevel;
    urgencyAdjustments?: {
      bonus: number;
      etaPenalty: number;
      originalScore: number;
    };
  };
}

/**
 * Driver location update for real-time tracking
 */
export interface DriverLocationUpdate {
  lat: number;
  lng: number;
  heading?: number;
  speed?: number;
  timestamp: string;
}

// =============================================================================
// RIDE INTERFACES
// =============================================================================

/**
 * Core ride request information
 */
export interface RideRequest {
  rideId: string;
  riderId: string;
  productId: string;
  pickupLocation: LocationPoint;
  destinationLocation: LocationPoint;
  stops?: LocationPoint[];
  requestedAt: Date;
  mode: RideMode;
  scheduledFor?: Date; // For later rides
  estimatedFare?: number;
  specialRequirements?: string[];
  constraints?: {
    maxDrivers?: number;
    maxSearchRadius?: number;
    timeoutMs?: number;
  };
}

/**
 * Ride status update information
 */
export interface RideStatusUpdate {
  rideId: string;
  status: RideStatus;
  message: string;
  timestamp: string;
  metadata?: {
    driverId?: string;
    driverName?: string;
    driverPhoto?: string;
    driverRating?: number;
    vehicleInfo?: {
      make: string;
      model: string;
      color: string;
      licensePlate: string;
    };
    eta?: {
      seconds: number;
      text: string;
    };
    location?: {
      lat: number;
      lng: number;
      heading?: number;
    };
    algorithm?: string;
    searchRadius?: number;
    processingTime?: number;
    reason?: string;
  };
}

// =============================================================================
// RIDE OFFER INTERFACES
// =============================================================================

/**
 * Data structure for creating ride offers
 */
export interface CreateRideOfferData {
  rideId: string;
  riderId: string;
  driverId: string;
  batchNumber: number;
  score: number;
  algorithm: string;
  expiresAt: Date;
  metadata?: Record<string, any>;
}

/**
 * Response structure for ride offer operations
 */
export interface RideOfferResponse {
  offerId: string;
  accepted: boolean;
  reason?: string;
}

/**
 * Real-time offer notification data sent to drivers
 */
export interface RideOfferNotificationData {
  rideId: string;
  offerId: string;
  driverId: string;
  riderId: string;
  pickupLocation: DetailedLocation;
  destinationLocation: DetailedLocation;
  fareEstimate: FareEstimate;
  estimatedArrival: EstimatedArrival;
  estimatedFare: number; // For backward compatibility
  estimatedDuration: number; // For backward compatibility (in minutes)
  expiresAt: string; // ISO string
  score: number;
  batchNumber: number;
  metadata?: Record<string, any>;
}

/**
 * Statistics for ride offer analytics
 */
export interface RideOfferStatistics {
  totalOffers: number;
  acceptedOffers: number;
  rejectedOffers: number;
  expiredOffers: number;
  acceptanceRate: number;
  averageResponseTime: number;
}

// =============================================================================
// BATCH PROCESSING INTERFACES
// =============================================================================

/**
 * Batch state tracking
 */
export interface BatchState {
  rideId: string;
  batchNumber: number;
  driverIds: string[];
  startedAt: Date;
  expiresAt: Date;
  status: BatchStatus;
}

/**
 * Result of a single batch processing
 */
export interface BatchResult {
  accepted: boolean;
  driverId?: string;
  batchNumber: number;
  processingTimeMs?: number;
}

/**
 * Result structure for batch offer processing
 */
export interface BatchOfferResult {
  batchNumber: number;
  totalOffers: number;
  acceptedOfferId?: string;
  acceptedDriverId?: string;
  acceptedAt?: Date;
  processingTimeMs: number;
  expired: boolean;
}

/**
 * Overall ride matching result
 */
export interface RideMatchResult {
  success: boolean;
  acceptedDriverId?: string;
  batchNumber?: number;
  totalBatches: number;
  processingTimeMs: number;
  reason?: string;
  rankedDrivers?: DriverScore[]; // Available when matching succeeds
}

/**
 * Batching configuration
 */
export interface BatchingConfig {
  acceptWindowMs: number; // Default: 10000 (10 seconds)
  maxBatches: number; // Default: 10
  totalTimeoutMs: number; // Default: 300000 (5 minutes)
  batchSizePercentage: number; // Default: 0.15 (15%)
  maxBatchSize: number; // Default: 5
}

// =============================================================================
// EVENT INTERFACES
// =============================================================================

/**
 * Ride requested event data
 */
export interface RideRequestedEventData {
  rideId: string;
  riderId: string;
  productId: string;
  pickupLocation: LocationPoint;
  destinationLocation: LocationPoint;
  stops?: LocationPoint[];
  requestedAt: string;
  estimatedFare?: number;
  specialRequirements?: string[];
  mode: RideMode;
  scheduledFor?: string; // For later rides
}

/**
 * Ride matched event data
 */
export interface RideMatchedEventData {
  rideId: string;
  riderId: string;
  driverId: string;
  matchedAt: string;
  estimatedArrival: EstimatedArrival;
  fareEstimate: FareEstimate;
}

/**
 * Ride cancelled event data
 */
export interface RideCancelledEventData {
  rideId: string;
  riderId: string;
  driverId?: string;
  cancelledBy: CancellationSource;
  reason: string;
  cancelledAt: string;
}

/**
 * Ride acceptance event
 */
export interface RideAcceptanceEvent {
  rideId: string;
  driverId: string;
  offerId: string;
  batchNumber: number;
  acceptedAt: Date;
}

/**
 * Ride acceptance data with full driver information
 */
export interface RideAcceptanceData {
  rideId: string;
  riderId: string;
  driverId: string;
  offerId: string;
  acceptedAt: string;
  estimatedArrival: {
    durationSeconds: number;
    arrivalTime: string;
  };
  driver: {
    name: string;
    rating: number;
    vehicleInfo: {
      make: string;
      model: string;
      color: string;
      licensePlate: string;
    };
    location: {
      lat: number;
      lng: number;
    };
  };
  correlationId: string;
  responseTimeMs: number;
}

// =============================================================================
// ALGORITHM INTERFACES
// =============================================================================

/**
 * Scoring context for driver evaluation
 */
export interface ScoringContext {
  rideId: string;
  pickupLocation: LocationPoint;
  destinationLocation: LocationPoint;
  pickupZoneId?: string;
  destinationZoneId?: string;
  requestedAt: Date;
  productId: string;
}

/**
 * Matching algorithm configuration
 */
export interface AlgorithmConfig {
  maxDrivers?: number;
  scoringPhase?: 'core' | 'extended';
  customWeights?: {
    eta?: number;
    idleTime?: number;
    rating?: number;
    acceptRate?: number;
    endingNearby?: number;
    zoneTransition?: number;
    goingHomeAlign?: number;
    newDriverBonus?: number;
  };
  penaltyConfig?: {
    batchNonResponsePenalty?: number;
    penaltyDecayRate?: number;
    penaltyTTL?: number;
    recentRejectionPenalty?: number;
    maxRecentRejectionPenalty?: number;
    rejectionTimeWindow?: number;
  };
}

// =============================================================================
// ANALYTICS AND METRICS INTERFACES
// =============================================================================

/**
 * Matching metrics for analytics
 */
export interface MatchingMetrics {
  rideId: string;
  totalDriversConsidered: number;
  totalBatches: number;
  successfulBatch?: number;
  processingTimeMs: number;
  algorithmUsed: string;
  averageDriverScore: number;
  acceptanceRate: number;
}

/**
 * Penalty configuration for services
 */
export interface PenaltyConfig {
  batchNonResponsePenalty: number; // Default: 15 points
  penaltyDecayRate: number; // Default: 0.7 (30% reduction per batch)
  penaltyTTL: number; // Default: 1800 seconds (30 minutes)
  recentRejectionPenalty: number; // Default: 2 points per rejection
  maxRecentRejectionPenalty: number; // Default: 10 points
  rejectionTimeWindow: number; // Default: 3600 seconds (1 hour)
}

/**
 * Driver penalty statistics
 */
export interface DriverPenaltyStats {
  totalPenaltiesApplied: number;
  averagePenaltyPoints: number;
  recentRejections: number;
  lastPenaltyAt?: Date;
}

// =============================================================================
// UTILITY TYPE DEFINITIONS
// =============================================================================

/**
 * Generic result wrapper for operations that can succeed or fail
 */
export interface OperationResult<T = void> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}

/**
 * Pagination metadata
 */
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Paginated response wrapper
 */
export interface PaginatedResponse<T> {
  data: T[];
  meta: PaginationMeta;
}

export interface RideData {
  rideId: string;
  riderId: string;
  productId: string;
  pickupLocation: {
    lat: number;
    lng: number;
    address: string;
  };
  destinationLocation: {
    lat: number;
    lng: number;
    address: string;
  };
  requestedAt: string;
  mode?: RideMode;
  scheduledFor?: string;
  distance?: number;
  duration?: number;
  stops?:
    | Array<{
        lat: number;
        lng: number;
        address: string;
      }>
    | undefined;
}
