import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { DriverDto } from '../../algorithm/src';
import { RideData } from '../interfaces';
import { GoogleRouteMatrixService } from '@shared/shared/common/google/google-route-matrix.service';
// import { RideMatchCacheRepository } from '@shared/shared/repositories/ride-match-cache.repository';
import {
  RideOfferRepository,
  CreateRideOfferData,
} from '@shared/shared/repositories/ride-offer.repository';

export interface BatchProcessingResult {
  rideId: string;
  batchNumber: number;
  totalBatches: number;
  success: boolean;
  notificationResults: Array<{
    driverId: string;
    success: boolean;
    channels: string[];
  }>;
  processingTimeMs: number;
  nextAction: 'next_batch' | 'expand_radius' | 'complete' | 'failed';
  reason?: string;
  acceptedDriverId?: string | undefined;
}

export interface BatchConfiguration {
  batchSize: number;
  batchTimeoutMs: number;
  maxBatches: number;
  enableRadiusExpansion: boolean;
  maxRadius: number;
  batchDelayMs: number;
}

@Injectable()
export class BatchOrchestratorService {
  private readonly logger = new Logger(BatchOrchestratorService.name);
  private readonly activeBatches = new Map<
    string,
    {
      timeoutId: NodeJS.Timeout;
      batchNumber: number;
      startTime: number;
      notifiedDrivers: string[];
    }
  >();

  private readonly DEFAULT_CONFIG: BatchConfiguration = {
    batchSize: 10,
    batchTimeoutMs: 10000, // 10 seconds
    maxBatches: 5,
    enableRadiusExpansion: true,
    maxRadius: 8, // 8km maximum radius (increased from 4km)
    batchDelayMs: 0, // No delay by default
  };

  constructor(
    // private readonly rideMatchCache: RideMatchCacheRepository,
    private readonly googleRouteMatrixService: GoogleRouteMatrixService,
    private readonly rideOfferRepository: RideOfferRepository,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Process a single batch asynchronously with callback
   */
  async processBatchAsync(
    rideId: string,
    riderId: string,
    drivers: any[],
    batchNumber: number,
    totalBatches: number,
    rideData: any,
    config: BatchConfiguration,
    onComplete: (accepted: boolean, acceptedDriverId?: string) => Promise<void>,
  ): Promise<void> {
    // const startTime = Date.now();

    try {
      this.logger.log(
        `BATCH_ASYNC_START: Processing batch ${batchNumber}/${totalBatches} ` +
          `for ride ${rideId} with ${drivers.length} drivers`,
      );

      // Save ride offers to database
      const savedOffers = await this.saveRideOffers(
        rideId,
        riderId,
        drivers,
        batchNumber,
        'priority',
      );

      // Create driver offers for event emission
      const driverOffers = await this.createDriverOffersFromSaved(
        savedOffers,
        rideData,
        config.batchTimeoutMs,
      );

      // Emit driver offers via event system (this will trigger NotifierService)
      await this.emitDriverOffers(driverOffers, batchNumber, totalBatches);

      // Set up async acceptance listener instead of blocking wait
      this.setupAsyncAcceptanceListener(
        rideId,
        drivers.map((d) => d.driverId),
        config.batchTimeoutMs,
        onComplete,
      );

      this.logger.log(
        `BATCH_ASYNC_SETUP: Batch ${batchNumber} notifications sent via events, ` +
          `waiting for acceptance (timeout: ${config.batchTimeoutMs}ms)`,
      );
    } catch (error) {
      this.logger.error(
        `Async batch processing failed for ride ${rideId}:`,
        error,
      );
      await onComplete(false);
    }
  }

  /**
   * Set up async acceptance listener with timeout
   */
  private setupAsyncAcceptanceListener(
    rideId: string,
    driverIds: string[],
    timeoutMs: number,
    onComplete: (accepted: boolean, acceptedDriverId?: string) => Promise<void>,
  ): void {
    let isResolved = false;

    const safeResolve = async (accepted: boolean, driverId?: string) => {
      if (isResolved) return;
      isResolved = true;

      // Clean up active batch tracking
      this.activeBatches.delete(rideId);

      // Clean up listener
      this.eventEmitter.off('driver.ride.accepted', acceptanceListener);

      // Clear timeout
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      await onComplete(accepted, driverId);
    };

    // Track active batch
    this.activeBatches.set(rideId, {
      timeoutId: null as any, // Will be set below
      batchNumber: this.activeBatches.size + 1,
      startTime: Date.now(),
      notifiedDrivers: driverIds,
    });

    // Set up timeout
    const timeoutId = setTimeout(async () => {
      this.logger.debug(
        `BATCH_TIMEOUT: Batch timeout reached for ride ${rideId} after ${timeoutMs}ms`,
      );
      await safeResolve(false);
    }, timeoutMs);

    // Update the timeout ID in active batch
    const activeBatch = this.activeBatches.get(rideId);
    if (activeBatch) {
      activeBatch.timeoutId = timeoutId;
    }

    // Set up acceptance listener
    const acceptanceListener = async (data: {
      rideId: string;
      driverId: string;
    }) => {
      if (data.rideId === rideId && driverIds.includes(data.driverId)) {
        this.logger.log(
          `BATCH_ACCEPTED: Driver ${data.driverId} accepted ride ${rideId}`,
        );
        await safeResolve(true, data.driverId);
      }
    };

    this.eventEmitter.on('driver.ride.accepted', acceptanceListener);

    this.logger.debug(
      `BATCH_LISTENER_SETUP: Set up acceptance listener for ride ${rideId} ` +
        `with ${driverIds.length} drivers (timeout: ${timeoutMs}ms)`,
    );
  }

  // Keep existing synchronous method for backward compatibility
  async processBatches(
    rideId: string,
    riderId: string,
    driverGroups: DriverDto[][],
    rideData: RideData,
    config?: Partial<BatchConfiguration>,
  ): Promise<BatchProcessingResult> {
    // This method is now deprecated but kept for compatibility
    // New async flow should use processBatchAsync
    const finalConfig = { ...this.DEFAULT_CONFIG, ...config };

    return new Promise(async (resolve) => {
      if (driverGroups.length === 0) {
        resolve({
          rideId,
          batchNumber: 0,
          totalBatches: 0,
          success: false,
          notificationResults: [],
          processingTimeMs: 0,
          nextAction: 'failed',
          reason: 'No driver groups provided',
        });
        return;
      }

      // Process only first batch for compatibility
      const drivers = driverGroups[0];
      const startTime = Date.now();

      await this.processBatchAsync(
        rideId,
        riderId,
        drivers,
        1,
        driverGroups.length,
        rideData,
        finalConfig,
        async (accepted: boolean, acceptedDriverId?: string) => {
          resolve({
            rideId,
            batchNumber: 1,
            totalBatches: driverGroups.length,
            success: accepted,
            notificationResults: drivers.map((driver) => ({
              driverId: driver.driverId,
              success: true,
              channels: ['websocket', 'push_notification'],
            })),
            processingTimeMs: Date.now() - startTime,
            nextAction: accepted ? 'complete' : 'next_batch',
            acceptedDriverId,
          });
        },
      );
    });
  }

  /**
   * Attempt radius expansion when no drivers accept
   */
  // private async attemptRadiusExpansion(
  //   rideId: string,
  //   _riderId: string,
  //   _rideData: any,
  //   _config: BatchConfiguration,
  // ): Promise<BatchProcessingResult> {
  //   try {
  //     this.logger.log(`Attempting radius expansion for ride ${rideId}`);

  //     // This would typically trigger a new search cycle
  //     // For now, return expansion indication
  //     return {
  //       rideId,
  //       batchNumber: 0,
  //       totalBatches: 0,
  //       success: false,
  //       notificationResults: [],
  //       processingTimeMs: 0,
  //       nextAction: 'expand_radius',
  //       reason: 'Radius expansion required - new driver search needed',
  //     };
  //   } catch (error) {
  //     this.logger.error(`Radius expansion failed for ride ${rideId}:`, error);

  //     return {
  //       rideId,
  //       batchNumber: 0,
  //       totalBatches: 0,
  //       success: false,
  //       notificationResults: [],
  //       processingTimeMs: 0,
  //       nextAction: 'failed',
  //       reason: `Radius expansion failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
  //     };
  //   }
  // }

  /**
   * Create driver offer data for notifications from saved offers (with IDs)
   */
  private async createDriverOffersFromSaved(
    savedOffers: any[],
    rideData: RideData,
    timeoutMs: number,
  ): Promise<any[]> {
    try {
      const expiresAt = new Date(Date.now() + timeoutMs).toISOString();

      // Get trip duration (compute once for all offers)
      const tripInfo =
        await this.googleRouteMatrixService.computePickupToDestination(
          {
            lat: Number(rideData.pickupLocation.lat),
            lng: Number(rideData.pickupLocation.lng),
          },
          {
            lat: Number(rideData.destinationLocation.lat),
            lng: Number(rideData.destinationLocation.lng),
          },
        );

      const driverOffers: any[] = savedOffers.map((offer) => {
        const metadata = offer.metadata || {};

        return {
          ...rideData,
          offerId: offer.id,
          driverId: offer.driverId,
          estimatedDuration: {
            toPickup: metadata.etaMinutes || 0,
            tripInfo: tripInfo,
          },
          etaToPickup: metadata.etaToPickup || metadata.etaMinutes || null,
          driverMetadata: {
            driverId: offer.driverId,
            etaMinutes: metadata.etaMinutes,
            etaToPickup: metadata.etaToPickup,
            ranking: metadata.ranking,
            distance: metadata.distance,
            score: offer.score,
            cellId: metadata.cellId,
          },
          // Fix property names to match expected structure
          pickup: rideData.pickupLocation,
          destination: rideData.destinationLocation,
          fareEstimate: {
            baseFare: 0,
            distanceFare: 0,
            timeFare: 0,
            totalFare: 0,
          },
          expiresAt,
          rank: metadata.ranking || 0,
          batchNumber: offer.batchNumber,
          score: offer.score,
          algorithm: offer.algorithm,
          offeredAt: offer.offeredAt,
        };
      });

      this.logger.debug(
        `Created ${driverOffers.length} driver offers from saved offers with IDs for notifications`,
      );

      return driverOffers;
    } catch (error) {
      this.logger.error(
        'Failed to create driver offers from saved offers:',
        error,
      );
      throw error;
    }
  }

  /**
   * Cancel active batch for a ride
   */
  public async cancelActiveBatch(rideId: string) {
    const activeBatch = this.activeBatches.get(rideId);
    if (activeBatch && activeBatch.timeoutId) {
      clearTimeout(activeBatch.timeoutId);
      this.activeBatches.delete(rideId);

      this.logger.log(
        `IMMEDIATE_BATCH_CANCEL: Cancelled active batch for ride ${rideId} - timeout cleared and state removed`,
      );

      // Emit batch cancelled event for other services to react
      this.eventEmitter.emit('ride.batch.cancelled', {
        rideId,
        timestamp: new Date().toISOString(),
        reason: 'Ride cancellation - immediate interruption',
        correlationId: `batch_cancel_${rideId}_${Date.now()}`,
      });
    }
  }

  /**
   * Handle driver acceptance during batch processing
   */
  async handleDriverAcceptance(
    rideId: string,
    driverId: string,
  ): Promise<boolean> {
    try {
      const activeBatch = this.activeBatches.get(rideId);

      if (!activeBatch) {
        this.logger.error(
          `BATCH_CONFLICT_NO_ACTIVE_BATCH: Driver ${driverId} accepted ride ${rideId} ` +
            `but no active batch exists | Possible race condition or timeout`,
        );
        return false;
      }

      if (!activeBatch.notifiedDrivers.includes(driverId)) {
        this.logger.error(
          `BATCH_CONFLICT_INVALID_DRIVER: Driver ${driverId} accepted ride ${rideId} ` +
            `but not in current batch [${activeBatch.notifiedDrivers.join(', ')}] | ` +
            `Batch: ${activeBatch.batchNumber}, Start: ${new Date(activeBatch.startTime).toISOString()}`,
        );
        return false;
      }

      this.logger.log(
        `BATCH_ACCEPTANCE_VALID: Driver ${driverId} valid acceptance for ride ${rideId} ` +
          `in batch ${activeBatch.batchNumber} | Response time: ${Date.now() - activeBatch.startTime}ms`,
      );

      // Emit acceptance event to resolve waiting batch
      this.eventEmitter.emit('ride.accepted', { rideId, driverId });

      // Notify other drivers in batch about timeout
      const otherDrivers = activeBatch.notifiedDrivers.filter(
        (id) => id !== driverId,
      );

      for (const otherDriverId of otherDrivers) {
        this.eventEmitter.emit('driver.offer.timeout', {
          driverId: otherDriverId,
          offerId: `${rideId}-batch-${activeBatch.batchNumber}`,
          rideId,
          reason: 'Another driver accepted the ride',
          timestamp: new Date().toISOString(),
          correlationId: `offer_timeout_${otherDriverId}_${Date.now()}`,
        });
      }

      this.logger.log(
        `Handled driver acceptance: ${driverId} accepted ride ${rideId} in batch ${activeBatch.batchNumber}`,
      );

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to handle driver acceptance for ride ${rideId}, driver ${driverId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Get batch processing statistics
   */
  async getBatchStatistics(): Promise<{
    activeBatches: number;
    totalBatchesProcessed: number;
    averageBatchTime: number;
    acceptanceRate: number;
  }> {
    // TODO: Implement proper metrics collection
    return {
      activeBatches: this.activeBatches.size,
      totalBatchesProcessed: 0,
      averageBatchTime: 0,
      acceptanceRate: 0,
    };
  }

  /**
   * Clean up expired batches (maintenance task)
   */
  async cleanupExpiredBatches(): Promise<number> {
    const currentTime = Date.now();
    let cleanedUp = 0;

    for (const [rideId, batch] of this.activeBatches.entries()) {
      // If batch is older than 5 minutes, clean it up
      if (currentTime - batch.startTime > 5 * 60 * 1000) {
        this.cancelActiveBatch(rideId);
        cleanedUp++;
      }
    }

    if (cleanedUp > 0) {
      this.logger.log(`Cleaned up ${cleanedUp} expired batches`);
    }

    return cleanedUp;
  }

  /**
   * Save ride offers to database
   */
  private async saveRideOffers(
    rideId: string,
    riderId: string,
    driverObjs: DriverDto[],
    batchNumber: number,
    algorithm: string,
  ): Promise<any[]> {
    try {
      const timeoutMs = this.DEFAULT_CONFIG.batchTimeoutMs;
      const expiresAt = new Date(Date.now() + timeoutMs);

      const offerData: CreateRideOfferData[] = driverObjs.map((driver) => ({
        rideId,
        riderId,
        driverId: driver.driverId,
        batchNumber,
        score: driver.ranking || driver.ranking || 0,
        algorithm,
        expiresAt,
        metadata: {
          etaMinutes: driver.etaMinutes,
          etaToPickup: driver.etaToPickup,
          ranking: driver.ranking,
        },
      }));

      const savedOffers =
        await this.rideOfferRepository.createBatchOffers(offerData);

      this.logger.log(
        `Successfully saved ${savedOffers.length} ride offers for ride ${rideId}, batch ${batchNumber}`,
      );

      return savedOffers;
    } catch (error) {
      this.logger.error(
        `Failed to save ride offers for ride ${rideId}, batch ${batchNumber}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Cancel all pending offers for a ride
   */
  async cancelRideOffers(rideId: string): Promise<number> {
    try {
      const cancelledCount =
        await this.rideOfferRepository.cancelRideOffers(rideId);

      this.logger.log(
        `Cancelled ${cancelledCount} pending offers for ride ${rideId}`,
      );

      return cancelledCount;
    } catch (error) {
      this.logger.error(
        `Failed to cancel ride offers for ride ${rideId}:`,
        error,
      );
      return 0;
    }
  }

  /**
   * Get ride offers for a specific ride
   */
  async getRideOffers(rideId: string): Promise<any[]> {
    try {
      const offers =
        await this.rideOfferRepository.getActiveOffersForRide(rideId);
      return offers;
    } catch (error) {
      this.logger.error(`Failed to get ride offers for ride ${rideId}:`, error);
      return [];
    }
  }

  /**
   * Get batch offers for a specific batch
   */
  async getBatchOffers(rideId: string, batchNumber: number): Promise<any[]> {
    try {
      const offers = await this.rideOfferRepository.getBatchOffers(
        rideId,
        batchNumber,
      );
      return offers;
    } catch (error) {
      this.logger.error(
        `Failed to get batch offers for ride ${rideId}, batch ${batchNumber}:`,
        error,
      );
      return [];
    }
  }

  /**
   * Emit driver offers via event system for realtime delivery
   */
  private async emitDriverOffers(
    driverOffers: any[],
    batchNumber: number,
    totalBatches: number,
  ): Promise<void> {
    let successfulEmissions = 0;
    let failedEmissions = 0;
    const failedOffers: string[] = [];

    try {
      this.logger.log(
        `NOTIFICATION_EMISSION_START: Emitting ${driverOffers.length} driver offers for batch ${batchNumber}/${totalBatches} ` +
          `(rideId=${driverOffers[0]?.rideId})`,
      );

      // Emit individual driver offers (will be caught by NotifierService)
      for (const offer of driverOffers) {
        try {
          const emissionData = {
            driverId: offer.driverId,
            offerId: offer.offerId,
            rideId: offer.rideId,
            riderId: offer.riderId,
            pickupLocation: {
              lat: offer.pickup?.lat || offer.pickupLocation?.lat,
              lng: offer.pickup?.lng || offer.pickupLocation?.lng,
              address: offer.pickup?.address || offer.pickupLocation?.address,
            },
            destinationLocation: {
              lat: offer.destination?.lat || offer.destinationLocation?.lat,
              lng: offer.destination?.lng || offer.destinationLocation?.lng,
              address:
                offer.destination?.address ||
                offer.destinationLocation?.address,
            },
            stops: offer.stops || [],
            duration: offer.duration || null,
            distance: offer.distance || null,
            fareEstimate: offer.fareEstimate,
            estimatedDuration: offer.estimatedDuration || null,
            etaToPickup: offer.etaToPickup,
            expiresAt: offer.expiresAt,
            batchNumber,
            totalBatches,
            timestamp: new Date().toISOString(),
            correlationId: `driver_offer_${offer.offerId}_${Date.now()}`,
          };

          this.eventEmitter.emit('driver.offer.created', emissionData);

          successfulEmissions++;
          this.logger.debug(
            `NOTIFICATION_SENT_SUCCESS: Driver offer emitted for driver ${offer.driverId} ` +
              `(offerId=${offer.offerId}, batch=${batchNumber}, eta=${offer.etaToPickup || offer.estimatedDuration?.toPickup}m)`,
          );
        } catch (driverError) {
          failedEmissions++;
          failedOffers.push(offer.driverId);
          this.logger.error(
            `NOTIFICATION_EMISSION_FAILED: Failed to emit offer for driver ${offer.driverId} ` +
              `(offerId=${offer.offerId}, batch=${batchNumber}):`,
            driverError,
          );
        }
      }

      // Emit batch offers sent event (will be caught by NotifierService)
      try {
        const batchEventData = {
          rideId: driverOffers[0]?.rideId,
          riderId: driverOffers[0]?.riderId,
          batchNumber,
          totalBatches,
          offerCount: driverOffers.length,
          successfulEmissions,
          failedEmissions,
          failedDriverIds: failedOffers,
          driverIds: driverOffers.map((o) => o.driverId),
          offerIds: driverOffers.map((o) => o.offerId),
          timestamp: new Date().toISOString(),
          correlationId: `batch_offers_${driverOffers[0]?.rideId}_${batchNumber}_${Date.now()}`,
        };

        this.eventEmitter.emit('driver.offers.batch.sent', batchEventData);

        this.logger.debug(
          `BATCH_NOTIFICATION_SUMMARY: Batch ${batchNumber}/${totalBatches} for ride ${driverOffers[0]?.rideId} - ` +
            `${successfulEmissions} successful, ${failedEmissions} failed ` +
            `(failed drivers: [${failedOffers.slice(0, 5).join(', ')}...])`,
        );
      } catch (batchError) {
        this.logger.error(
          `BATCH_EVENT_EMISSION_FAILED: Failed to emit batch notification event for batch ${batchNumber}:`,
          batchError,
        );
        failedEmissions++;
      }

      if (failedEmissions > 0) {
        this.logger.warn(
          `NOTIFICATION_DELIVERY_WARNING: Batch ${batchNumber} had ${failedEmissions}/${driverOffers.length} ` +
            `emission failures - potential delivery issues to drivers: [${failedOffers.slice(0, 10).join(', ')}...]`,
        );

        // If more than 50% failed, consider this a partial failure
        if (failedEmissions > driverOffers.length * 0.5) {
          this.logger.error(
            `NOTIFICATION_DELIVERY_CRITICAL: Batch ${batchNumber} had critical failure rate ` +
              `(${((failedEmissions / driverOffers.length) * 100).toFixed(1)}%) - consider retrying or expanding radius`,
          );
        }
      }
    } catch (error) {
      this.logger.error(
        `Failed to emit driver offers for batch ${batchNumber}:`,
        error,
      );
      throw error;
    }
  }
}
