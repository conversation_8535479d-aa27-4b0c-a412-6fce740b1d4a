import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';
import { TaxGroup, TaxGroupRepository } from '@shared/shared/repositories';


export interface CreateTaxGroupData {
  name: string;
  description?: string;
  subcategories: {
    name: string;
    percentage: number;
  }[];
}

export interface UpdateTaxGroupData {
  name?: string;
  description?: string;
  subcategories?: {
    id?: string;
    name: string;
    percentage: number;
  }[];
}

@Injectable()
export class TaxGroupService {
  constructor(
    private readonly taxGroupRepository: TaxGroupRepository,
    private readonly prisma: PrismaService,
  ) { }

  /**
   * Create a new tax group with subcategories.
   * @param data - Tax group creation data
   */
  async createTaxGroup(data: CreateTaxGroupData): Promise<TaxGroup> {
    // Validate input
    this.validateTaxGroupData(data);

    // Check for duplicate name
    const existingTaxGroup = await this.taxGroupRepository.findTaxGroupByName(data.name);
    if (existingTaxGroup && !existingTaxGroup.deletedAt) {
      throw new BadRequestException(`Tax group with name "${data.name}" already exists`);
    }

    // Calculate total percentage
    const totalPercentage = data.subcategories.reduce((sum, sub) => sum + sub.percentage, 0);

    // Create tax group and subcategories in a transaction
    return this.prisma.$transaction(async (tx) => {
      // Create tax group
      const taxGroup = await tx.taxGroup.create({
        data: {
          name: data.name,
          description: data.description ?? null,
          totalPercentage,
          isActive: true,
        },
      });

      // Create subcategories
      const subcategories = await Promise.all(
        data.subcategories.map((sub) =>
          tx.taxSubcategory.create({
            data: {
              taxGroupId: taxGroup.id,
              name: sub.name,
              percentage: sub.percentage,
            },
          }),
        ),
      );

      return {
        ...taxGroup,
        totalPercentage: typeof taxGroup.totalPercentage === 'object' && 'toNumber' in taxGroup.totalPercentage
          ? (taxGroup.totalPercentage as any).toNumber()
          : taxGroup.totalPercentage,
        subcategories: subcategories.map(sub => ({
          ...sub,
          percentage: typeof sub.percentage === 'object' && 'toNumber' in sub.percentage
            ? (sub.percentage as any).toNumber()
            : sub.percentage,
        })),
      } as TaxGroup;
    });
  }

  /**
   * Find all tax groups.
   */
  async findAllTaxGroups(): Promise<TaxGroup[]> {
    return this.taxGroupRepository.findAllTaxGroups();
  }

  /**
   * Find tax group by ID.
   * @param id - Tax group ID
   */
  async findTaxGroupById(id: string): Promise<TaxGroup> {
    const taxGroup = await this.taxGroupRepository.findTaxGroupById(id);
    if (!taxGroup) {
      throw new NotFoundException(`Tax group with ID ${id} not found`);
    }
    return taxGroup;
  }

  /**
   * Update tax group by ID.
   * @param id - Tax group ID
   * @param data - Updated tax group data
   */
  async updateTaxGroup(id: string, data: UpdateTaxGroupData): Promise<TaxGroup> {
    // Check if tax group exists
    const existingTaxGroup = await this.findTaxGroupById(id);

    // Validate input if subcategories are provided
    if (data.subcategories) {
      this.validateSubcategories(data.subcategories);
    }

    // Check for duplicate name (excluding current record)
    if (data.name && data.name !== existingTaxGroup.name) {
      const duplicateTaxGroup = await this.taxGroupRepository.findTaxGroupByName(data.name);
      if (duplicateTaxGroup && duplicateTaxGroup.id !== id) {
        throw new BadRequestException(`Tax group with name "${data.name}" already exists`);
      }
    }

    // Update tax group and subcategories in a transaction
    return this.prisma.$transaction(async (tx) => {
      let totalPercentage = existingTaxGroup.totalPercentage;

      // Handle subcategories update if provided
      if (data.subcategories) {
        // Get existing subcategories
        await tx.taxSubcategory.findMany({
          where: { taxGroupId: id, deletedAt: null },
        });

        // Soft delete all existing subcategories
        await tx.taxSubcategory.updateMany({
          where: { taxGroupId: id, deletedAt: null },
          data: { deletedAt: new Date(), updatedAt: new Date() },
        });

        // Create new subcategories
        await Promise.all(
          data.subcategories.map((sub) =>
            tx.taxSubcategory.create({
              data: {
                taxGroupId: id,
                name: sub.name,
                percentage: sub.percentage,
              },
            }),
          ),
        );

        // Recalculate total percentage
        totalPercentage = data.subcategories.reduce((sum, sub) => sum + sub.percentage, 0);
      }

      // Update tax group
      const updatedTaxGroup = await tx.taxGroup.update({
        where: { id },
        data: {
          ...(data.name && { name: data.name }),
          ...(data.description !== undefined && { description: data.description }),
          totalPercentage,
          updatedAt: new Date(),
        },
        include: {
          subcategories: {
            where: { deletedAt: null },
            orderBy: { createdAt: 'asc' },
          },
        },
      });

      return {
        ...updatedTaxGroup,
        totalPercentage: typeof updatedTaxGroup.totalPercentage === 'object' && 'toNumber' in updatedTaxGroup.totalPercentage
          ? (updatedTaxGroup.totalPercentage as any).toNumber()
          : updatedTaxGroup.totalPercentage,
        subcategories: updatedTaxGroup.subcategories.map(sub => ({
          ...sub,
          percentage: typeof sub.percentage === 'object' && 'toNumber' in sub.percentage
            ? (sub.percentage as any).toNumber()
            : sub.percentage,
        })),
      } as TaxGroup;
    });
  }

  /**
   * Delete tax group by ID.
   * @param id - Tax group ID
   */
  async deleteTaxGroup(id: string): Promise<TaxGroup> {
    // Check if tax group exists
    await this.findTaxGroupById(id);

    // Check if tax group is in use
    const isInUse = await this.taxGroupRepository.isTaxGroupInUse(id);
    if (isInUse) {
      throw new BadRequestException('This tax group is in use and cannot be deleted');
    }

    // Soft delete tax group (subcategories will be cascade deleted)
    return this.taxGroupRepository.deleteTaxGroup(id);
  }

  /**
   * Get paginated tax groups.
   * @param paginationDto - Pagination parameters
   */
  async paginateTaxGroups(paginationDto: any) {
    const { page = 1, limit = 10, search } = paginationDto;
    return this.taxGroupRepository.paginateTaxGroups(page, limit, { search });
  }

  /**
   * Get active tax groups.
   */
  async getActiveTaxGroups(): Promise<TaxGroup[]> {
    return this.taxGroupRepository.findTaxGroupsByActiveStatus(true);
  }

  /**
   * Toggle tax group active status.
   * @param id - Tax group ID
   * @param isActive - New active status
   */
  async toggleTaxGroupStatus(id: string, isActive: boolean): Promise<TaxGroup> {
    await this.findTaxGroupById(id);
    return this.taxGroupRepository.updateTaxGroup(id, { isActive });
  }

  /**
   * Validate tax group creation data.
   * @param data - Tax group data
   */
  private validateTaxGroupData(data: CreateTaxGroupData): void {
    if (!data.name || data.name.trim().length < 2) {
      throw new BadRequestException('Tax group name must be at least 2 characters long');
    }

    if (!data.subcategories || data.subcategories.length === 0) {
      throw new BadRequestException('At least one subcategory is required');
    }

    this.validateSubcategories(data.subcategories);
  }

  /**
   * Validate subcategories data.
   * @param subcategories - Subcategories data
   */
  private validateSubcategories(subcategories: { name: string; percentage: number }[]): void {
    for (const sub of subcategories) {
      if (!sub.name || sub.name.trim().length < 1) {
        throw new BadRequestException('Subcategory name is required');
      }

      if (typeof sub.percentage !== 'number' || sub.percentage <= 0) {
        throw new BadRequestException('Subcategory percentage must be a positive number');
      }

      if (sub.percentage > 100) {
        throw new BadRequestException('Subcategory percentage cannot exceed 100%');
      }

      // Check for max 2 decimal places
      if (Number(sub.percentage.toFixed(2)) !== sub.percentage) {
        throw new BadRequestException('Subcategory percentage can have at most 2 decimal places');
      }
    }

    // Check for duplicate subcategory names
    const names = subcategories.map(sub => sub.name.toLowerCase().trim());
    const uniqueNames = new Set(names);
    if (names.length !== uniqueNames.size) {
      throw new BadRequestException('Subcategory names must be unique within a tax group');
    }
  }
}
