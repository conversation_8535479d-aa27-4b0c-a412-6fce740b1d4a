import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { Ride } from './models/ride.model';
import { PrismaService } from '../database/prisma/prisma.service';
import { RideStatus } from '@shared/shared/modules/ride-matching/constants';

@Injectable()
export class RideRepository extends BaseRepository<Ride> {
  protected readonly modelName = 'ride';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new ride
   */
  async createRide(
    data: Omit<Ride, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Ride> {
    return this.create(data);
  }

  /**
   *  Create ride with lifecycle in single transaction
   */
  async createRideWithLifecycle(
    rideData: Omit<Ride, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
    lifecycleData: any,
  ): Promise<Ride> {
    return this.prisma.$transaction(async (tx) => {
      // Create the ride (only include fields that exist in the Prisma model)
      const ride = await tx.ride.create({
        data: {
          riderId: rideData.riderId,
          productId: rideData.productId,
          status: rideData.status,
          pickupLocation: rideData.pickupLocation as any,
          destinationLocation: rideData.destinationLocation as any,
          stops: rideData.stops ? (rideData.stops as any) : undefined,
          driverId: rideData.driverId || null,
          verificationCode: rideData.verificationCode || null,
          createdAt: new Date(),
          updatedAt: new Date(),
          duration: rideData.duration || null,
          distance: rideData.distance || null,
        },
      });

      // Create the lifecycle entry
      await tx.rideLifecycle.create({
        data: {
          ...lifecycleData,
          rideId: ride.id,
          createdAt: new Date(),
        },
      });

      // Cast the return value to our Ride interface
      return {
        ...ride,
        status: ride.status as RideStatus,
        pickupLocation: ride.pickupLocation as any,
        destinationLocation: ride.destinationLocation as any,
        stops: ride.stops as any,
      } as Ride;
    });
  }

  /**
   * Find rides by rider ID
   */
  async findRidesByRiderId(
    riderId: string,
    excludeStatuses?: RideStatus[],
  ): Promise<Ride[]> {
    const whereClause: any = {
      riderId,
    };

    if (excludeStatuses && excludeStatuses.length > 0) {
      whereClause.status = {
        notIn: excludeStatuses,
      };
    }

    return this.findMany({
      where: whereClause,
      include: {
        driver: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
        product: {
          select: {
            name: true,
            icon: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  /**
   * Find rides by driver ID
   */
  async findRidesByDriverId(
    driverId: string,
    excludeStatuses?: RideStatus[],
  ): Promise<Ride[]> {
    const whereClause: any = {
      driverId,
    };

    if (excludeStatuses && excludeStatuses.length > 0) {
      whereClause.status = {
        notIn: excludeStatuses,
      };
    }

    return this.findMany({
      where: whereClause,
      include: {
        rider: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
        product: {
          select: {
            name: true,
            icon: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  /**
   * Find ride by ID with full details
   */
  async findRideByIdWithDetails(id: string): Promise<Ride | null> {
    return this.findById(id, {
      include: {
        driver: {
          include: {
            user: true,
            role: true,
          },
        },
        product: {
          include: {
            vehicleType: true,
          },
        },
      },
    });
  }

  /**
   * Find ride by ID with full details
   */
  async findRideByIdWithDetailsRider(id: string): Promise<Ride | null> {
    return this.findById(id, {
      include: {
        driver: {
          select: {
            firstName: true,
            lastName: true,
            profilePictureUrl: true,
            metaData: {
              select: {
                avgRating: true,
              },
            },
          },
        },
        product: {
          select: {
            name: true,
            icon: true,
          },
        },
        rider: {
          select: {
            id: true,
            rideOtp: true,
            firstName: true,
            lastName: true,
          },
        },
        driverVehicle: {
          select: {
            vehicleNumber: true,
            vehicleType: {
              select: {
                name: true,
              },
            },
          },
        },
        rideLifecycles: {
          select: {
            status: true,
            createdAt: true,
            location: true,
            meta: true,
          },
          orderBy: {
            createdAt: 'asc',
          },
        },
      },
    });
  }

  async findRideByIdWithDetailsById(id: string): Promise<Ride | null> {
    return this.findById(id, {
      include: {
        driver: {
          select: {
            firstName: true,
            lastName: true,
            profilePictureUrl: true,
            metaData: {
              select: {
                avgRating: true,
              },
            },
          },
        },
        product: {
          select: {
            name: true,
            icon: true,
          },
        },
        rider: {
          select: {
            id: true,
            rideOtp: true,
            firstName: true,
            lastName: true,
          },
        },
        driverVehicle: {
          select: {
            vehicleNumber: true,
            vehicleType: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });
  }

  /**
   * Find ride by ID with rider details for driver acceptance
   */
  async findRideByIdWithRiderDetails(id: string): Promise<Ride | null> {
    return this.findById(id, {
      include: {
        rider: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profilePictureUrl: true,
            rideOtp: true,
          },
        },
      },
    });
  }

  /**
   * Update ride status
   */
  async updateRideStatus(
    id: string,
    status: RideStatus,
    additionalData?: Partial<Ride>,
  ): Promise<Ride> {
    const updateData = {
      ...additionalData,
      status: status,
    };
    const updatedRide = await this.model.update({
      where: { id },
      data: updateData,
    });
    return {
      ...updatedRide,
      status: updatedRide.status as RideStatus,
      pickupLocation: updatedRide.pickupLocation as any,
      destinationLocation: updatedRide.destinationLocation as any,
      stops: updatedRide.stops as any,
    } as Ride;
  }

  /**
   * Assign driver to ride
   */
  async assignDriverToRide(rideId: string, driverId: string): Promise<Ride> {
    const updatedRide = await this.model.update({
      where: { id: rideId },
      data: {
        driverId,
        status: RideStatus.ACCEPTED,
      },
    });
    return {
      ...updatedRide,
      status: updatedRide.status as RideStatus,
      pickupLocation: updatedRide.pickupLocation as any,
      destinationLocation: updatedRide.destinationLocation as any,
      stops: updatedRide.stops as any,
    } as Ride;
  }

  /**
   * Complete ride
   */
  async completeRide(rideId: string): Promise<Ride> {
    const updatedRide = await this.model.update({
      where: { id: rideId },
      data: {
        status: RideStatus.TRIP_COMPLETED,
        completedAt: new Date(),
      },
    });
    return {
      ...updatedRide,
      status: updatedRide.status as RideStatus,
      pickupLocation: updatedRide.pickupLocation as any,
      destinationLocation: updatedRide.destinationLocation as any,
      stops: updatedRide.stops as any,
      completedAt: updatedRide.completedAt,
    } as Ride;
  }

  /**
   * Get rides by status
   */
  async findRidesByStatus(status: RideStatus): Promise<Ride[]> {
    return this.findMany({
      where: { status: status },
      include: {
        driver: {
          include: {
            user: true,
            role: true,
          },
        },
        rider: {
          include: {
            user: true,
            role: true,
          },
        },
        product: {
          include: {
            vehicleType: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  /**
   * Find rides by driver ID with pagination and status filtering
   */
  async findRidesByDriverIdPaginated(
    driverId: string,
    statuses?: RideStatus[],
    page: number = 1,
    limit: number = 10,
    fromDate?: Date,
    toDate?: Date,
  ): Promise<{ rides: Ride[]; total: number }> {
    let whereClause: any = {
      driverId,
    };

    if (statuses && statuses.length > 0) {
      whereClause = {
        ...whereClause,
        status: {
          in: statuses,
        },
      };
    }

    if (fromDate || toDate) {
      whereClause.createdAt = {};
      if (fromDate) {
        whereClause.createdAt.gte = fromDate;
      }
      if (toDate) {
        whereClause.createdAt.lte = toDate;
      }
    }

    const [rides, total] = await Promise.all([
      this.findMany({
        where: whereClause,
        include: {
          product: {
            select: {
              name: true,
              icon: true,
            },
          },
          rider: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      this.count(whereClause), // ✅ pass only `whereClause`
    ]);

    return { rides, total };
  }

  /**
   * Find rides by rider ID with pagination and status filtering
   */
  async findRidesByRiderIdPaginated(
    riderId: string,
    statuses?: RideStatus[],
    page: number = 1,
    limit: number = 10,
    fromDate?: Date,
    toDate?: Date,
  ): Promise<{ rides: Ride[]; total: number }> {
    let whereClause: any = {
      riderId,
    };

    if (statuses && statuses.length > 0) {
      whereClause = {
        ...whereClause,
        status: {
          in: statuses,
        },
      };
    }

    if (fromDate || toDate) {
      whereClause.createdAt = {};
      if (fromDate) {
        whereClause.createdAt.gte = fromDate;
      }
      if (toDate) {
        whereClause.createdAt.lte = toDate;
      }
    }

    const [rides, total] = await Promise.all([
      this.findMany({
        where: whereClause,
        include: {
          product: {
            select: {
              name: true,
              icon: true,
            },
          },
          driver: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
          driverVehicle: {
            select: {
              vehicleNumber: true,
              vehicleType: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      this.count(whereClause),
    ]);

    return { rides, total };
  }

  /**
   * Find ride by ID with driver details for driver view
   */
  async findRideByIdWithDriverDetails(id: string): Promise<Ride | null> {
    return this.findById(id, {
      include: {
        rider: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profilePictureUrl: true,
          },
        },
        driver: {
          select: {
            metaData: {
              select: {
                avgRating: true,
              },
            },
          },
        },
        product: {
          select: {
            name: true,
            icon: true,
          },
        },
        driverVehicle: {
          select: {
            vehicleNumber: true,
            vehicleType: {
              select: {
                name: true,
              },
            },
          },
        },
        rideLifecycles: {
          select: {
            status: true,
            createdAt: true,
            location: true,
            meta: true,
          },
          orderBy: {
            createdAt: 'asc',
          },
        },
      },
    });
  }

  /**
   * Find rides for admin with comprehensive filtering and pagination
   */
  async findRidesForAdminPaginated(filters: {
    status?: string | undefined;
    riderName?: string | undefined;
    driverName?: string | undefined;
    riderId?: string | undefined;
    driverId?: string | undefined;
    productId?: string | undefined;
    page?: number;
    limit?: number;
    fromDate?: Date | undefined;
    toDate?: Date | undefined;
    vehicleNumber?: string | undefined;
  }): Promise<{ rides: any[]; total: number }> {
    const {
      status,
      riderName,
      driverName,
      riderId,
      driverId,
      productId,
      page = 1,
      limit = 10,
      fromDate,
      toDate,
      vehicleNumber,
    } = filters;

    let whereClause: any = {};

    // Status filter
    if (status) {
      whereClause.status = status;
    }

    // User ID filters
    if (riderId) {
      whereClause.riderId = riderId;
    }

    if (driverId) {
      whereClause.driverId = driverId;
    }

    // Product filter
    if (productId) {
      whereClause.productId = productId;
    }

    // Date range filter
    if (fromDate || toDate) {
      whereClause.createdAt = {};
      if (fromDate) {
        whereClause.createdAt.gte = fromDate;
      }
      if (toDate) {
        whereClause.createdAt.lte = toDate;
      }
    }

    // Name search filters
    if (riderName) {
      whereClause.rider = {
        OR: [
          { firstName: { contains: riderName, mode: 'insensitive' } },
          { lastName: { contains: riderName, mode: 'insensitive' } },
        ],
      };
    }

    if (driverName) {
      whereClause.driver = {
        OR: [
          { firstName: { contains: driverName, mode: 'insensitive' } },
          { lastName: { contains: driverName, mode: 'insensitive' } },
        ],
      };
    }
    if (vehicleNumber) {
      whereClause.driverVehicle = {
        vehicleNumber: { contains: vehicleNumber, mode: 'insensitive' },
      };
    }

    const [rides, total] = await Promise.all([
      this.findMany({
        where: whereClause,
        include: {
          rider: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
          driver: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
          product: {
            select: {
              id: true,
              name: true,
              icon: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      this.count(whereClause),
    ]);

    return { rides, total };
  }

  /**
   * Find ride by ID with comprehensive details for admin view
   */
  async findRideByIdForAdmin(id: string): Promise<any | null> {
    return this.findById(id, {
      include: {
        rider: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profilePictureUrl: true,
            user: {
              select: {
                email: true,
                phoneNumber: true,
              },
            },
          },
        },
        driver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profilePictureUrl: true,
            user: {
              select: {
                email: true,
                phoneNumber: true,
              },
            },
          },
        },
        product: {
          select: {
            id: true,
            name: true,
            description: true,
            icon: true,
          },
        },
        driverVehicle: {
          include: {
            vehicleType: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });
  }
}
