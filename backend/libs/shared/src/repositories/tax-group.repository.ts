import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma/prisma.service';
import { BaseRepository, FindManyOptions } from './base.repository';
import { TaxGroup } from './models/taxGroup.model';

@Injectable()
export class TaxGroupRepository extends BaseRepository<TaxGroup> {
  protected readonly modelName = 'taxGroup';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new tax group record.
   * @param data - Tax group data excluding id and timestamps
   */
  async createTaxGroup(
    data: Omit<TaxGroup, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<TaxGroup> {
    return this.create(data);
  }

  /**
   * Find all tax groups with subcategories.
   */
  async findAllTaxGroups(): Promise<TaxGroup[]> {
    return this.findMany({
      include: {
        subcategories: {
          where: { deletedAt: null },
          orderBy: { createdAt: 'asc' },
        },
      },
    });
  }

  /**
   * Find tax group by ID with subcategories.
   * @param id - Tax group ID
   */
  async findTaxGroupById(id: string): Promise<TaxGroup | null> {
    return this.findById(id, {
      include: {
        subcategories: {
          where: { deletedAt: null },
          orderBy: { createdAt: 'asc' },
        },
      },
    });
  }

  /**
   * Find tax group by name (case-insensitive).
   * @param name - Tax group name
   */
  async findTaxGroupByName(name: string): Promise<TaxGroup | null> {
    return this.findOne({
      where: {
        name: {
          equals: name,
          mode: 'insensitive',
        },
      },
    });
  }

  /**
   * Update tax group by ID.
   * @param id - Tax group ID
   * @param data - Updated tax group data
   */
  async updateTaxGroup(
    id: string,
    data: Partial<Omit<TaxGroup, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>,
  ): Promise<TaxGroup> {
    return this.update({
      where: { id },
      data,
      include: {
        subcategories: {
          where: { deletedAt: null },
          orderBy: { createdAt: 'asc' },
        },
      },
    });
  }

  /**
   * Delete tax group by ID (soft delete).
   * @param id - Tax group ID
   */
  async deleteTaxGroup(id: string): Promise<TaxGroup> {
    return this.softDelete({ where: { id } });
  }

  /**
   * Get paginated tax groups with search functionality.
   * @param page - Page number
   * @param limit - Items per page
   * @param options - Additional options including search
   */
  async paginateTaxGroups(
    page: number = 1,
    limit: number = 10,
    options?: FindManyOptions & { search?: string },
  ) {
    const { search, ...restOptions } = options || {};

    let whereClause: any = {};

    if (search) {
      whereClause = {
        OR: [
          {
            name: {
              contains: search,
              mode: 'insensitive',
            },
          },
          {
            description: {
              contains: search,
              mode: 'insensitive',
            },
          },
        ],
      };
    }

    return this.paginate(page, limit, {
      where: whereClause,
      include: {
        subcategories: {
          where: { deletedAt: null },
          orderBy: { createdAt: 'asc' },
        },
      },
      orderBy: { createdAt: 'desc' },
      ...restOptions,
    });
  }

  /**
   * Check if tax group is referenced in any pricing rules or other entities.
   * @param id - Tax group ID
   */
  async isTaxGroupInUse(_id: string): Promise<boolean> {
    // TODO: Add checks for references in pricing rules or other entities
    // For now, return false as there are no references implemented yet
    return false;
  }

  /**
   * Get tax groups by active status.
   * @param isActive - Active status filter
   */
  async findTaxGroupsByActiveStatus(isActive: boolean): Promise<TaxGroup[]> {
    return this.findMany({
      where: { isActive },
      include: {
        subcategories: {
          where: { deletedAt: null },
          orderBy: { createdAt: 'asc' },
        },
      },
      orderBy: { name: 'asc' },
    });
  }
}
