'use client';

import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useState } from 'react';
import { useDetachCharge } from '../api/charge-mutations';
import { Charge, CHARGE_TYPE_LABELS, PRICE_MODEL_LABELS } from '../types/charge';
import { ChargeModal } from './charge-modal';
import { ChargeDeleteModal } from './charge-delete-modal';
import { EditPriorityModal } from './edit-priority-modal';
import { Spinner } from '@/components/ui/spinner';

const getColumns = ({
   detachChargeMutation,
   handleEditClick,
   handleDeleteClick,
   handleEditPriorityClick,
   chargeToDelete,
}: {
   handleEditClick: (charge: Charge) => void;
   handleDeleteClick: (charge: Charge) => void;
   handleEditPriorityClick: (charge: Charge) => void;
   detachChargeMutation: any;
   chargeToDelete: Charge | null;
}): ColumnDef<Charge>[] => [
   {
      accessorKey: 'name',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Name</div>,
      cell: ({ row }) => {
         const charge = row.original as Charge;
         return (
            <div className='text-left max-w-[200px]'>
               <div className='text-sm font-medium break-words'>{charge.name}</div>
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'identifier',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Identifier</div>,
      cell: ({ row }) => {
         const charge = row.original as Charge;
         return (
            <div className='text-left max-w-[150px]'>
               <div className='text-sm text-gray-600 break-words'>{charge.identifier || '-'}</div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'chargeType',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Charge Type</div>
      ),
      cell: ({ row }) => {
         const charge = row.original as Charge;
         return (
            <div className='text-left'>
               <div className='text-sm text-gray-600'>{CHARGE_TYPE_LABELS[charge.chargeType]}</div>
            </div>
         );
      },
      size: 120,
   },
   // {
   //   accessorKey: 'meter',
   //   header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Meter</div>,
   //   cell: ({ row }) => {
   //     const charge = row.original as Charge;
   //     return (
   //       <div className='text-left max-w-[150px]'>
   //         <div className='text-sm text-gray-600'>
   //           {charge.meter ? CHARGE_METER_LABELS[charge.meter] : '-'}
   //         </div>
   //       </div>
   //     );
   //   },
   //   size: 150,
   // },
   {
      accessorKey: 'priceModel',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Price Model</div>
      ),
      cell: ({ row }) => {
         const charge = row.original as Charge;
         return (
            <div className='text-left max-w-[150px]'>
               <div className='text-sm text-gray-600'>{PRICE_MODEL_LABELS[charge.priceModel]}</div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'price',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Price</div>,
      cell: ({ row }) => {
         const charge = row.original as Charge;
         const { price } = charge;

         // Display price based on price model
         let priceDisplay = '-';
         if (price) {
            if (price.amount !== undefined) {
               priceDisplay = `${price.currency || ''} ${price.amount}`.trim();
            } else if (price.rate !== undefined) {
               priceDisplay = `${price.currency || ''} ${price.rate}/unit`.trim();
            } else if (price.tiers && price.tiers.length > 0) {
               priceDisplay = `${price.tiers.length} tier(s)`;
            } else if (price.formula) {
               priceDisplay = 'Formula';
            }
         }

         if (charge.percentage !== undefined && charge.percentage !== null) {
            priceDisplay = `${charge.percentage}%`;
         }

         return (
            <div className='text-left max-w-[150px]'>
               <div className='text-sm text-gray-600'>{priceDisplay}</div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'priority',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Priority</div>,
      cell: ({ row }) => {
         const charge = row.original as Charge;
         return (
            <div className='text-left'>
               <div className='text-sm text-gray-600'>{charge.priority ?? '-'}</div>
            </div>
         );
      },
      size: 10,
   },
   {
      id: 'actions',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const charge = row.original as Charge;
         const isDeleting = chargeToDelete?.id === charge.id && detachChargeMutation.isPending;

         return (
            <div className='flex justify-center gap-1'>
               <button
                  className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => handleEditClick(charge)}
                  disabled={isDeleting}
               >
                  Edit
               </button>
               <button
                  className='text-sm font-medium text-purple-600 hover:text-purple-700 border border-purple-300 hover:border-purple-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => handleEditPriorityClick(charge)}
                  disabled={isDeleting}
               >
                  Priority
               </button>
               <button
                  className='text-sm font-medium text-red-600 hover:text-red-700 border border-red-300 hover:border-red-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => handleDeleteClick(charge)}
                  disabled={isDeleting}
               >
                  {isDeleting ? '...' : 'Delete'}
               </button>
            </div>
         );
      },
      size: 280,
   },
];

interface ChargeTableProps {
  chargeGroupId: string;
  data: Charge[] | undefined;
  isLoading: boolean;
}

export function ChargeTable({ chargeGroupId, data, isLoading }: ChargeTableProps) {
  const [chargeToEdit, setChargeToEdit] = useState<Charge | null>(null);
  const [chargeToDelete, setChargeToDelete] = useState<Charge | null>(null);
  const [chargeToEditPriority, setChargeToEditPriority] = useState<Charge | null>(null);
  const detachChargeMutation = useDetachCharge();
  const queryClient = useQueryClient();

  const handleEditClick = (charge: Charge) => {
    setChargeToEdit(charge);
  };

  const handleDeleteClick = (charge: Charge) => {
    setChargeToDelete(charge);
  };

  const handleEditPriorityClick = (charge: Charge) => {
    setChargeToEditPriority(charge);
  };

  const handleDeleteConfirm = () => {
    if (!chargeToDelete) return;

    detachChargeMutation.mutate(
      { chargeGroupId, chargeId: chargeToDelete.id },
      {
        onSuccess: () => {
          toast.success('Charge detached successfully');
          queryClient.invalidateQueries({ queryKey: ['charges', chargeGroupId] });
        },
        onSettled: () => {
          setChargeToDelete(null);
        },
      }
    );
  };

  const columns = getColumns({
    detachChargeMutation,
    handleEditClick,
    handleDeleteClick,
    handleEditPriorityClick,
    chargeToDelete,
  });

  const table = useReactTable({
    data: data || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (isLoading) {
    return (
      <div className='flex items-center justify-center py-8'>
        <Spinner className='h-8 w-8' />
      </div>
    );
  }

  if (!data?.length) {
    return (
      <div className='text-center py-12 border rounded-md bg-gray-50'>
        <p className='text-gray-500 mb-4'>No charges found in this group</p>
        <p className='text-sm text-gray-400'>Click "Add Charge" to create your first charge</p>
      </div>
    );
  }

  return (
    <div className='space-y-2'>
      <div className='rounded-md border'>
        <div className='overflow-x-auto'>
          <table className='w-full table-fixed'>
            <thead>
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id} className='border-b bg-gray-50'>
                  {headerGroup.headers.map((header) => (
                    <th
                      key={header.id}
                      className='h-11 px-4 text-left align-middle'
                      style={{ width: header.getSize(), maxWidth: header.getSize() }}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody>
              {table.getRowModel().rows.map((row) => (
                <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                  {row.getVisibleCells().map((cell) => (
                    <td
                      key={cell.id}
                      className='px-4 py-3 align-middle'
                      style={{
                        width: cell.column.getSize(),
                        maxWidth: cell.column.getSize(),
                      }}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <ChargeDeleteModal
        isOpen={!!chargeToDelete}
        onClose={() => setChargeToDelete(null)}
        onConfirm={handleDeleteConfirm}
        isLoading={detachChargeMutation.isPending}
        chargeName={chargeToDelete?.name || ''}
      />

      {/* Edit Modal */}
      <ChargeModal
        mode='edit'
        chargeGroupId={chargeGroupId}
        chargeId={chargeToEdit?.id || null}
        chargeData={chargeToEdit || undefined}
        isOpen={!!chargeToEdit}
        onClose={() => setChargeToEdit(null)}
      />

      {/* Edit Priority Modal */}
      <EditPriorityModal
        chargeGroupId={chargeGroupId}
        chargeGroupChargeId={chargeToEditPriority?.chargeGroupChargeId || null}
        currentPriority={chargeToEditPriority?.priority ?? 0}
        chargeName={chargeToEditPriority?.name || ''}
        isOpen={!!chargeToEditPriority}
        onClose={() => setChargeToEditPriority(null)}
      />
    </div>
  );
}