'use client';

import { useState, useEffect, useMemo } from 'react';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import MultipleSelector, { Option } from '@/components/ui/multiselect';
import { Spinner } from '@/components/ui/spinner';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { useListChargeGroup } from '@/modules/charge-group/api/queries';
import {
   useAttachChargeGroups,
   useDetachChargeGroup,
   useUpdateChargeGroupPriorities,
} from '../api/city-product-fare-mutations';
import { useGetFareChargeGroups } from '../api/city-product-fare-queries';
import { Settings } from 'lucide-react';

interface ChargeGroupOption extends Option {
   chargeGroupId: string;
}

interface AttachChargeGroupsModalProps {
   fareId: string;
   open: boolean;
   onOpenChange: (open: boolean) => void;
}

export function AttachChargeGroupsModal({ fareId, open, onOpenChange }: AttachChargeGroupsModalProps) {
   const [selectedOptions, setSelectedOptions] = useState<ChargeGroupOption[]>([]);
   const [initialChargeGroupIds, setInitialChargeGroupIds] = useState<string[]>([]);
   const [priorities, setPriorities] = useState<Record<string, number>>({});
   const [initialPriorities, setInitialPriorities] = useState<Record<string, number>>({});
   const [isSubmitting, setIsSubmitting] = useState(false);

   const queryClient = useQueryClient();

   const fareChargeGroupsQuery = useGetFareChargeGroups(fareId, open) as any; // API returns FareChargeGroupRelation[] not FareChargeGroup[]
   const allChargeGroupsQuery = useListChargeGroup({
      page: 1,
      limit: 100,
      sortBy: 'name',
      sortOrder: 'asc',
      enabled: open,
   });
   const attachMutation = useAttachChargeGroups();
   const detachMutation = useDetachChargeGroup();
   const updatePrioritiesMutation = useUpdateChargeGroupPriorities();

   // Convert all charge groups to MultipleSelector Option format
   const chargeGroupOptions: ChargeGroupOption[] = useMemo(() => {
      return (allChargeGroupsQuery.data?.data || []).map(cg => ({
         value: cg.id,
         label: cg.name,
         chargeGroupId: cg.id,
      }));
   }, [allChargeGroupsQuery.data?.data]);

   // Set initial selected options when fare charge groups load
   useEffect(() => {
      if (fareChargeGroupsQuery.data?.data && allChargeGroupsQuery.data?.data && open) {
         const attachedRelations = fareChargeGroupsQuery.data.data; // This is FareChargeGroupRelation[]

         // Extract charge group IDs from the relations
         const attachedIds = attachedRelations.map((relation: any) => relation.chargeGroupId);
         setInitialChargeGroupIds(attachedIds);

         // Map relations to options using the nested chargeGroup object
         const initialOptions: ChargeGroupOption[] = attachedRelations.map((relation: any) => ({
            value: relation.chargeGroup.id,
            label: relation.chargeGroup.name,
            chargeGroupId: relation.chargeGroup.id,
         }));
         setSelectedOptions(initialOptions);

         // Set initial priorities using chargeGroupId as key
         const initialPrioritiesData: Record<string, number> = {};
         attachedRelations.forEach((relation: any) => {
            initialPrioritiesData[relation.chargeGroup.id] = relation.priority;
         });
         setPriorities(initialPrioritiesData);
         setInitialPriorities(initialPrioritiesData);
      }
   }, [fareChargeGroupsQuery.data?.data, allChargeGroupsQuery.data?.data, open]);

   const handlePriorityChange = (chargeGroupId: string, value: string) => {
      const priority = parseInt(value, 10);
      if (!isNaN(priority) && priority >= 1) {
         setPriorities(prev => ({
            ...prev,
            [chargeGroupId]: priority,
         }));
      }
   };

   const handleSubmit = async () => {
      setIsSubmitting(true);
      try {
         const currentChargeGroupIds = selectedOptions.map(opt => opt.chargeGroupId);
         const toRemove = initialChargeGroupIds.filter(id => !currentChargeGroupIds.includes(id));
         const toAdd = currentChargeGroupIds.filter(id => !initialChargeGroupIds.includes(id));

         // Detect priority changes for existing charge groups
         const toUpdate = currentChargeGroupIds.filter(id => {
            // Only check existing charge groups (not newly added ones)
            if (!initialChargeGroupIds.includes(id)) return false;
            // Check if priority has changed
            return priorities[id] !== initialPriorities[id];
         });

         // Validate priorities for new selections
         for (const id of toAdd) {
            if (!priorities[id] || priorities[id] < 1) {
               toast.error(
                  `Please set a valid priority (≥ 1) for ${
                     selectedOptions.find(opt => opt.chargeGroupId === id)?.label
                  }`
               );
               setIsSubmitting(false);
               return;
            }
         }

         const promises: Promise<any>[] = [];

         // Remove charge groups
         for (const chargeGroupId of toRemove) {
            promises.push(
               detachMutation.mutateAsync({
                  fareId,
                  chargeGroupId,
               })
            );
         }

         // Add charge groups
         if (toAdd.length > 0) {
            const chargeGroupsToAdd = toAdd.map(id => ({
               chargeGroupId: id,
               priority: priorities[id] || 1,
            }));

            promises.push(
               attachMutation.mutateAsync({
                  fareId,
                  data: {
                     chargeGroups: chargeGroupsToAdd,
                  },
               })
            );
         }

         // Update priorities for existing charge groups
         if (toUpdate.length > 0) {
            const chargeGroupsToUpdate = toUpdate.map(id => ({
               chargeGroupId: id,
               priority: priorities[id],
            }));

            promises.push(
               updatePrioritiesMutation.mutateAsync({
                  fareId,
                  data: {
                     chargeGroups: chargeGroupsToUpdate,
                  },
               })
            );
         }

         await Promise.all(promises);

         // Invalidate queries
         queryClient.invalidateQueries({ queryKey: ['fare-charge-groups', fareId] });
         queryClient.invalidateQueries({ queryKey: ['city-product-fares'] });

         toast.success('Charge groups updated successfully');
         onOpenChange(false);
      } catch (error: any) {
         console.error('Error updating charge groups:', error);
      } finally {
         setIsSubmitting(false);
      }
   };

   const handleClose = () => {
      onOpenChange(false);
      // Reset to initial state
      if (fareChargeGroupsQuery.data?.data) {
         const attachedRelations = fareChargeGroupsQuery.data.data;
         const initialOptions: ChargeGroupOption[] = attachedRelations.map((relation: any) => ({
            value: relation.chargeGroup.id,
            label: relation.chargeGroup.name,
            chargeGroupId: relation.chargeGroup.id,
         }));
         setSelectedOptions(initialOptions);

         const initialPrioritiesData: Record<string, number> = {};
         attachedRelations.forEach((relation: any) => {
            initialPrioritiesData[relation.chargeGroup.id] = relation.priority;
         });
         setPriorities(initialPrioritiesData);
         setInitialPriorities(initialPrioritiesData);
      }
   };

   const isLoading = fareChargeGroupsQuery.isLoading || allChargeGroupsQuery.isLoading;

   const handleSelectionChange = (newOptions: Option[]) => {
      const updatedOptions: ChargeGroupOption[] = newOptions.map(opt => {
         // Find the option in chargeGroupOptions to preserve all properties
         const existingOption = chargeGroupOptions.find(cgo => cgo.value === opt.value);
         return (
            existingOption || {
               ...opt,
               value: opt.value,
               label: opt.label,
               chargeGroupId: (opt as any).chargeGroupId || opt.value,
            }
         );
      });
      setSelectedOptions(updatedOptions);

      // Initialize priorities for newly added options
      newOptions.forEach(opt => {
         if (!priorities[opt.value]) {
            setPriorities(prev => ({
               ...prev,
               [opt.value]: 1,
            }));
         }
      });
   };

   return (
      <Dialog open={open} onOpenChange={handleClose}>
         <DialogContent className='sm:max-w-[600px]'>
            <DialogHeader>
               <DialogTitle className='flex items-center gap-2'>
                  <Settings className='w-5 h-5' />
                  Manage Charge Groups
               </DialogTitle>
               <DialogDescription>
                  Add or delete charge groups for this fare rule and set their priorities.
               </DialogDescription>
            </DialogHeader>

            <div className='py-4 space-y-4'>
               {isLoading ? (
                  <div className='flex items-center justify-center py-8'>
                     <Spinner className='mr-2' />
                     <span className='text-sm text-gray-600'>Loading charge groups...</span>
                  </div>
               ) : (
                  <>
                     <div className='space-y-2'>
                        <Label htmlFor='chargeGroups'>Charge Groups</Label>
                        <MultipleSelector
                           value={selectedOptions}
                           onChange={handleSelectionChange}
                           defaultOptions={chargeGroupOptions}
                           placeholder='Select charge groups...'
                           emptyIndicator={
                              <p className='text-center text-sm text-gray-500'>
                                 No charge groups found
                              </p>
                           }
                           commandProps={{
                              label: 'Select charge groups',
                           }}
                        />
                        <div className='text-xs text-gray-500 mt-1'>
                           Selected: {selectedOptions.length} charge group
                           {selectedOptions.length !== 1 ? 's' : ''}
                        </div>
                     </div>

                     {selectedOptions.length > 0 && (
                        <div className='space-y-2'>
                           <Label>Set Priorities</Label>
                           <div className='border rounded-md p-3 space-y-2 max-h-60 overflow-y-auto'>
                              {selectedOptions.map(opt => (
                                 <div
                                    key={opt.chargeGroupId}
                                    className='flex items-center gap-3 justify-between'
                                 >
                                    <span className='text-sm text-gray-700 flex-1'>
                                       {opt.label}
                                    </span>
                                    <div className='flex items-center gap-2'>
                                       <Input
                                          type='number'
                                          min='1'
                                          value={priorities[opt.chargeGroupId] || 1}
                                          onChange={e =>
                                             handlePriorityChange(opt.chargeGroupId, e.target.value)
                                          }
                                          className='w-20'
                                          placeholder='Priority'
                                       />
                                    </div>
                                 </div>
                              ))}
                           </div>
                           <p className='text-xs text-gray-500'>
                              Lower priority values are executed first
                           </p>
                        </div>
                     )}
                  </>
               )}
            </div>

            <DialogFooter>
               <Button variant='outline' onClick={handleClose} disabled={isSubmitting}>
                  Cancel
               </Button>
               <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting || isLoading}
                  className='min-w-[120px]'
               >
                  {isSubmitting ? (
                     <div className='flex items-center gap-2'>
                        <Spinner size='sm' />
                        Updating...
                     </div>
                  ) : (
                     'Update Groups'
                  )}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}
