import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
   devIndicators: false,
   /* config options here */
   images: {
      remotePatterns: [
         {
            protocol: 'https',
            hostname: 'staging-test-tkx.s3.us-east-1.amazonaws.com',
         },
         {
            protocol: 'https',
            hostname: 'tuxi-staging-bucket.s3.ap-south-1.amazonaws.com',
         },
         {
            protocol: 'https',
            hostname: 'tukxi-staging-public.s3.ap-south-1.amazonaws.com',
         },
      ],
   },
};

export default nextConfig;
